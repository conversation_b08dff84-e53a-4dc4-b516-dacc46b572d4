// 数据归一化配置驱动方案
// 基于 demo.md 中的思路实现
// primitive 类型字段需要添加默认值

import { processNumberOrString } from './bus';

// 映射类型定义
// type FieldMappingType = 'primitive' | 'object' | 'array';

// 基础映射定义
interface BaseMapping {
  path: string; // 原始数据路径
  defaultValue?: any;
  transformer?: (value: any, sourceObject: any) => any; // 转换函数可以接收整个源对象以进行更复杂的计算
}

// 针对不同类型的映射定义
interface PrimitiveMapping extends BaseMapping {
  type: 'primitive';
}

interface ObjectMapping extends BaseMapping {
  type: 'object';
  // 如果是对象，我们需要一个子配置
  schema: { [key: string]: Mapping };
}

interface ArrayMapping extends BaseMapping {
  type: 'array';
  // 如果是数组，我们需要针对数组中每个元素的映射配置
  // 如果 schema 为空，表示数组元素是原始值（string、number等）
  schema?: { [key: string]: Mapping };
}

// 最终的映射可以是这三种中的任意一种
type Mapping = PrimitiveMapping | ObjectMapping | ArrayMapping;

// 辅助函数，用于安全地按路径获取值
function getValueByPath(obj: any, path: string): any {
  if (!path) return obj; // 如果路径为空，返回整个对象
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

// 强大的递归归一化引擎
export function normalizeDataRecursively<T>(sourceData: any, config: { [K in keyof T]: Mapping }): T {
  const normalized = {} as T;

  for (const key in config) {
    if (Object.prototype.hasOwnProperty.call(config, key)) {
      const mapping = config[key as keyof T];
      const { path, defaultValue, transformer } = mapping;

      let rawValue = getValueByPath(sourceData, path);

      // 如果值不存在，使用默认值
      if (rawValue === undefined || rawValue === null) {
        rawValue = defaultValue;
      }

      let finalValue = rawValue;

      // 根据映射类型进行处理
      //  'primitive' | 'object' | 'array'
      switch (mapping.type) {
        case 'primitive':
          // 如果有转换器，则使用它
          if (transformer) {
            finalValue = transformer(rawValue, sourceData);
          }
          break;

        case 'object':
          if (transformer) {
            finalValue = transformer(rawValue, sourceData);
          } else if (typeof rawValue === 'object' && rawValue !== null && !Array.isArray(rawValue)) {
            // 如果源值是对象，则递归地进行归一化
            finalValue = normalizeDataRecursively(rawValue, mapping.schema);
          } else {
            // 如果源值不是对象（或为null），则使用默认值或递归处理一个空对象以确保结构完整
            finalValue = normalizeDataRecursively(defaultValue || {}, mapping.schema);
          }
          break;

        case 'array':
          if (transformer) {
            finalValue = transformer(rawValue, sourceData);
          } else if (Array.isArray(rawValue)) {
            // 如果源值是数组，则遍历并对每个元素进行归一化
            if (mapping.schema) {
              // 有 schema 的情况，对每个元素进行递归处理
              finalValue = rawValue.map(item => normalizeDataRecursively(item, mapping.schema!));
            } else {
              // 没有 schema 的情况，数组元素是原始值，直接返回
              finalValue = rawValue;
            }
          } else {
            // 如果不是数组，则返回默认值（通常是空数组）
            finalValue = defaultValue ?? [];
          }
          break;
      }

      normalized[key as keyof T] = finalValue;
    }
  }

  return normalized;
}

// HOD数据转换器 - 处理联合类型
const hodBidAdjustmentTransformer = (hodData: {
  adjustments: {
    adjustment: number,
    hour: number,
  }[];
  rationale: string;
} | {
  hour: number;
  adjustment: number;
  rationale: string;
}[]): {
  adjustments: {
    adjustment: number,
    hour: number,
  }[];
  rationale: string;
} => {
  if (!hodData) {
    return {
      adjustments: [],
      rationale: ''
    };
  }

  // 检查是哪种结构
  if (Array.isArray(hodData)) {
    // 第一种结构: { hour, adjustment, rationale }[] -> 转换为对象格式
    return {
      adjustments: hodData.map(item => ({
        hour: item?.hour ?? -1,
        adjustment: item?.adjustment ?? 0
      })),
      rationale: hodData[0]?.rationale ?? ''
    };
  }

  // 第二种结构: { adjustments: [], rationale: string } -> 直接返回
  if (hodData.adjustments && Array.isArray(hodData.adjustments)) {
    return {
      adjustments: hodData.adjustments,
      rationale: hodData.rationale
    };
  }

  // 未知结构，返回空对象
  return {
    adjustments: [],
    rationale: ''
  };
};

// 为 WeekStrategyData 创建映射配置
export const weekStrategyConfig: { [K in keyof Strategy.WeekStrategyData]: Mapping } = {
  start_date: { type: 'primitive', path: 'start_date', defaultValue: '' },
  end_date: { type: 'primitive', path: 'end_date', defaultValue: '' },
  current_time: { type: 'primitive', path: 'current_time', defaultValue: '' },
  approach: { type: 'primitive', path: 'approach', defaultValue: '' },
  rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
  revision_history: { type: 'primitive', path: 'revision_history', defaultValue: {} },
  ads_suggestion: {
    type: 'object',
    path: 'ads_suggestion',
    schema: {
      approach: { type: 'primitive', path: 'approach', defaultValue: '' },
      primary_goal: {
        type: 'object',
        path: 'primary_goal',
        schema: {
          goal: { type: 'primitive', path: 'goal', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
        }
      },
      other_goals: { type: 'array', path: 'other_goals' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
    }
  },
  primary_goal: {
    type: 'object',
    path: 'primary_goal',
    schema: {
      goal: { type: 'primitive', path: 'goal', defaultValue: '' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
    }
  },
  other_goals: { type: 'array', path: 'other_goals' },
  week_budget: {
    type: 'object',
    path: 'week_budget',
    schema: {
      typical: { type: 'primitive', path: 'typical', defaultValue: '' },
      min: { type: 'primitive', path: 'min', defaultValue: '' },
      max: { type: 'primitive', path: 'max', defaultValue: '' },
      last_week_budget: { type: 'primitive', path: 'last_week_budget', defaultValue: '' },
      change_from_last_week: { type: 'primitive', path: 'change_from_last_week', defaultValue: '' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
    }
  },
  bid_adjustment_range: {
    type: 'object',
    path: 'bid_adjustment_range',
    schema: {
      min: { type: 'primitive', path: 'min', defaultValue: '' },
      max: { type: 'primitive', path: 'max', defaultValue: '' },
      rational: { type: 'primitive', path: 'rational', defaultValue: '' }
    }
  },
  // 动态键对象：{ [date: string]: { approach, guideline, budget_range, bid_adjustment_range } }
  daily_strategy_suggestion: { type: 'primitive', path: 'daily_strategy_suggestion', defaultValue: '' },
  week_expected_result: {
    type: 'object',
    path: 'week_expected_result',
    schema: {
      spend: {
        type: 'object',
        path: 'spend',
        schema: {
          typical: { type: 'primitive', path: 'typical', defaultValue: '' },
          min: { type: 'primitive', path: 'min', defaultValue: '' },
          max: { type: 'primitive', path: 'max', defaultValue: '' },
          last_week: { type: 'primitive', path: 'last_week', defaultValue: '' },
          change_from_last_week: { type: 'primitive', path: 'change_from_last_week', defaultValue: '' }
        }
      },
      sales: {
        type: 'object',
        path: 'sales',
        schema: {
          typical: { type: 'primitive', path: 'typical', defaultValue: '' },
          min: { type: 'primitive', path: 'min', defaultValue: '' },
          max: { type: 'primitive', path: 'max', defaultValue: '' },
          last_week: { type: 'primitive', path: 'last_week', defaultValue: '' },
          change_from_last_week: { type: 'primitive', path: 'change_from_last_week', defaultValue: '' }
        }
      },
      orders: {
        type: 'object',
        path: 'orders',
        schema: {
          typical: { type: 'primitive', path: 'typical', defaultValue: '' },
          min: { type: 'primitive', path: 'min', defaultValue: '' },
          max: { type: 'primitive', path: 'max', defaultValue: '' },
          last_week: { type: 'primitive', path: 'last_week', defaultValue: '' },
          change_from_last_week: { type: 'primitive', path: 'change_from_last_week', defaultValue: '' }
        }
      },
      cvr: {
        type: 'object',
        path: 'cvr',
        schema: {
          typical: { type: 'primitive', path: 'typical', defaultValue: '' },
          min: { type: 'primitive', path: 'min', defaultValue: '' },
          max: { type: 'primitive', path: 'max', defaultValue: '' },
          last_week: { type: 'primitive', path: 'last_week', defaultValue: '' },
          change_from_last_week: { type: 'primitive', path: 'change_from_last_week', defaultValue: '' }
        }
      },
      acos: {
        type: 'object',
        path: 'acos',
        schema: {
          typical: { type: 'primitive', path: 'typical', defaultValue: '' },
          min: { type: 'primitive', path: 'min', defaultValue: '' },
          max: { type: 'primitive', path: 'max', defaultValue: '' },
          last_week: { type: 'primitive', path: 'last_week', defaultValue: '' },
          change_from_last_week: { type: 'primitive', path: 'change_from_last_week', defaultValue: '' }
        }
      }
    }
  },
  tips_for_week_after_target_week: { type: 'primitive', path: 'tips_for_week_after_target_week', defaultValue: '' }
};

// 为 WeekAnalysisData 创建映射配置
export const weekAnalysisConfig: { [K in keyof Strategy.WeekAnalysisData]: Mapping } = {
  start_date: { type: 'primitive', path: 'start_date', defaultValue: '' },
  end_date: { type: 'primitive', path: 'end_date', defaultValue: '' },
  forecast: {
    type: 'object',
    path: 'forecast',
    schema: {
      overview: { type: 'primitive', path: 'overview', defaultValue: '' },
      market_preview: { type: 'array', path: 'market_preview' },
      metrics_forecast: {
        type: 'object',
        path: 'metrics_forecast',
        schema: {
          traffic: { type: 'primitive', path: 'traffic', defaultValue: '' },
          spend: { type: 'primitive', path: 'spend', defaultValue: '' },
          sales: { type: 'primitive', path: 'sales', defaultValue: '' },
          acos: { type: 'primitive', path: 'acos', defaultValue: '' },
          cvr: { type: 'primitive', path: 'cvr', defaultValue: '' }
        }
      }
    }
  },
  revision_history: { type: 'primitive', path: 'revision_history', defaultValue: {} },
  weekly_strategy: {
    type: 'array',
    path: 'weekly_strategy',
    schema: {
      week_start_date: { type: 'primitive', path: 'week_start_date', defaultValue: '' },
      strategy: { type: 'primitive', path: 'strategy', defaultValue: '' }
    }
  },
  key_dates: {
    type: 'array',
    path: 'key_dates',
    schema: {
      start_date: { type: 'primitive', path: 'start_date', defaultValue: '' },
      end_date: { type: 'primitive', path: 'end_date', defaultValue: '' },
      type: { type: 'array', path: 'type' },
      name: { type: 'primitive', path: 'name', defaultValue: '' },
      confidence: { type: 'primitive', path: 'confidence', defaultValue: '' },
      significance: { type: 'primitive', path: 'significance', defaultValue: '' },
      expected_impact: {
        type: 'object',
        path: 'expected_impact',
        schema: {
          traffic: { type: 'primitive', path: 'traffic', defaultValue: '' },
          conversion: { type: 'primitive', path: 'conversion', defaultValue: '' },
          competition: { type: 'primitive', path: 'competition', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
        }
      },
      strategy: { type: 'primitive', path: 'strategy', defaultValue: '' }
    }
  },
  swot: {
    type: 'object',
    path: 'swot',
    schema: {
      strengths: { type: 'array', path: 'strengths' },
      weaknesses: { type: 'array', path: 'weaknesses' },
      opportunities: { type: 'array', path: 'opportunities' },
      threats: { type: 'array', path: 'threats' }
    },
  },
  ads_suggestion: {
    type: 'object',
    path: 'ads_suggestion',
    schema: {
      approach: { type: 'primitive', path: 'approach', defaultValue: '' },
      primary_goal: {
        type: 'object',
        path: 'primary_goal',
        schema: {
          goal: { type: 'primitive', path: 'goal', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
        }
      },
      other_goals: { type: 'array', path: 'other_goals' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
      weekly_strategy: {
        type: 'array',
        path: 'weekly_strategy',
        schema: {
          week_start_date: { type: 'primitive', path: 'week_start_date', defaultValue: '' },
          strategy: { type: 'primitive', path: 'strategy', defaultValue: '' }
        }
      }
    }
  },
  non_ads_suggestion: { type: 'array', path: 'non_ads_suggestion' },
  google_searchs: {
    type: 'object',
    path: 'google_searchs',
    schema: {
      rendered_content: { type: 'primitive', path: 'rendered_content', defaultValue: '' },
      web_search_queries: { type: 'primitive', path: 'web_search_queries', defaultValue: '' },
      grounding_chunks: {
        type: 'array',
        path: 'grounding_chunks',
        schema: {
          title: { type: 'primitive', path: 'title', defaultValue: '' },
          url: { type: 'primitive', path: 'url', defaultValue: '' }
        }
      }
    }
  }
};

// 为 Real_ads_result 创建映射配置
export const realAdsResultConfig: { [K in keyof Strategy.Real_ads_result]: Mapping } = {
  acos: { type: 'primitive', path: 'acos', defaultValue: '' },
  clicks: { type: 'primitive', path: 'clicks', defaultValue: '' },
  cvr: { type: 'primitive', path: 'cvr', defaultValue: '' },
  impressions: { type: 'primitive', path: 'impressions', defaultValue: '' },
  orders: { type: 'primitive', path: 'orders', defaultValue: '' },
  sales: { type: 'primitive', path: 'sales', defaultValue: '' },
  spend: { type: 'primitive', path: 'spend', defaultValue: '' }
};

// 专门处理 Monthly_trends 的归一化函数
export function monthlyTrendsTransformer(data: any, sourceData: any): Strategy.MonthlyTrendItem[] {
  // 适配可能的字段名
  const _data = data || sourceData.market_trends.monthly_trends
  if (!_data || typeof _data !== 'object') {
    return [];
  }

  // 处理数组格式：[{"1": {"trend": "...", "demond": "85%"}}, {"2": {...}}, ...]
  if (Array.isArray(_data)) {
    if (_data[0] && typeof _data[0] === 'object' && _data[0]['1']) {
      return _data.map((item) => {
        if (item && typeof item === 'object') {
          const monthKey = Object.keys(item)[0]; // 获取月份键（"1", "2", 等）
          const monthData = item[monthKey];

          if (monthData && typeof monthData === 'object') {
            return {
              month: `${monthKey}月`,
              demond: processNumberOrString(monthData.demond || '0'),
              trend: monthData.trend || '',
            };
          }
        }
        return {
          month: '',
          demond: 0,
          trend: '',
        };
      }).filter(item => item.month); // 过滤掉无效的条目
    } else {
      return _data.map((item) => {
        return {
          ...item,
          demond: processNumberOrString(item.demond || item.demand_index || '0'),
          month: `${item.month}月`,
        }
      })
    }
  }

  // 处理对象格式：{"1": {"trend": "...", "demond": "85%"}, "2": {...}, ...}
  const result: Strategy.MonthlyTrendItem[] = [];
  for (const [month, monthData] of Object.entries(_data)) {
    if (monthData && typeof monthData === 'object') {
      const item = monthData as any;
      result.push({
        month: `${month}月`,
        demond: processNumberOrString(item.demond || item.demand || item.demand_index || '0'),
        trend: item.trend || '',
      });
    }
  }
  return result.length > 0 ? result : [];
}

export const marketTrendsConfig = {
  monthly_trends: {
    type: 'array',
    path: 'monthly_trends',
    schema: {
      month: { type: 'primitive', path: 'month', defaultValue: '' },
      demond: { type: 'primitive', path: 'demond', defaultValue: '' },
      trend: { type: 'primitive', path: 'trend', defaultValue: '' },
      strategy: { type: 'primitive', path: 'strategy', defaultValue: '' }
    },
    transformer: monthlyTrendsTransformer
  }
}

// 为 DayStrategyData 创建映射配置
export const dayStrategyConfig: { [K in keyof Strategy.DayStrategyData]: Mapping } = {
  date: {
    type: 'primitive',
    path: 'date',
    defaultValue: ''
  },
  approach: {
    type: 'primitive',
    path: 'approach',
    defaultValue: ''
  },
  rationale: {
    type: 'primitive',
    path: 'rationale',
    defaultValue: ''
  },
  // ---- 处理嵌套对象 ----
  day_budget: {
    type: 'object',
    path: 'day_budget', // API数据源
    schema: { // 子对象的映射规则
      amount: { type: 'primitive', path: 'amount', defaultValue: '' },
      yesterday: { type: 'primitive', path: 'yesterday', defaultValue: '' },
      change_from_yesterday: { type: 'primitive', path: 'change_from_yesterday', defaultValue: '' },
      adjustment_range: {
        type: 'object',
        path: 'adjustment_range',
        schema: {
          min: { type: 'primitive', path: 'min', defaultValue: '' },
          max: { type: 'primitive', path: 'max', defaultValue: '' },
        }
      },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
    }
  },
  bid_adjustment_range: {
    type: 'object',
    path: 'bid_adjustment_range',
    schema: {
      typical: { type: 'primitive', path: 'typical', defaultValue: '' },
      min: { type: 'primitive', path: 'min', defaultValue: '' },
      max: { type: 'primitive', path: 'max', defaultValue: '' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
    }
  },
  revision_history: {
    type: 'primitive', path: 'revision_history', defaultValue: {}
  },
  expected_results: {
    type: 'object',
    path: 'expected_results',
    schema: {
      spend: { type: 'primitive', path: 'spend', defaultValue: '' },
      sales: { type: 'primitive', path: 'sales', defaultValue: '' },
      cvr: { type: 'primitive', path: 'cvr', defaultValue: '' },
      acos: { type: 'primitive', path: 'acos', defaultValue: '' },
    }
  },
  // ---- 处理数组 ----
  campaign_budget: {
    type: 'array',
    path: 'campaign_budget', // API数据源数组
    schema: { // 数组内每个对象的映射规则
      campaign_id: { type: 'primitive', path: 'campaign_id', defaultValue: '' },
      amount: { type: 'primitive', path: 'amount', defaultValue: '' },
      benchmark_budget: { type: 'primitive', path: 'benchmark_budget', defaultValue: '' },
      change_from_weekly_avg: { type: 'primitive', path: 'change_from_weekly_avg', defaultValue: '' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
      bid_adjustment: { type: 'primitive', path: 'bid_adjustment', defaultValue: '' },
      campaign_name: { type: 'primitive', path: 'campaign_name', defaultValue: '' },
      campaign_type: { type: 'primitive', path: 'campaign_type', defaultValue: '' },
      l14d_acos: { type: 'primitive', path: 'l14d_acos', defaultValue: '' },
      swd_acos: { type: 'primitive', path: 'swd_acos', defaultValue: '' },
    }
  },
  // ---- 处理联合类型 ----
  hod_bid_adjustment: {
    type: 'primitive', // 我们把它当作一个需要整体转换的"原始"值
    path: 'hod_bid_adjustment',
    transformer: hodBidAdjustmentTransformer
  },
  placement_bid_adjustment: {
    type: 'array',
    path: 'placement_bid_adjustment',
    schema: {
      placement_type: { type: 'primitive', path: 'placement_type', defaultValue: '' },
      old_bid: { type: 'primitive', path: 'old_bid', defaultValue: '' },
      new_bid: { type: 'primitive', path: 'new_bid', defaultValue: '' },
      adjustment_ratio: { type: 'primitive', path: 'adjustment_ratio', defaultValue: '' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
    }
  },
  weekly_progress_analysis: {
    type: 'object',
    path: 'weekly_progress_analysis',
    schema: {
      budget: {
        type: 'object',
        path: 'budget',
        schema: {
          week_budget: { type: 'primitive', path: 'week_budget', defaultValue: '' },
          week_budget_used: { type: 'primitive', path: 'week_budget_used', defaultValue: '' },
          week_budget_left: { type: 'primitive', path: 'week_budget_left', defaultValue: '' },
          week_budget_usage_rate: { type: 'primitive', path: 'week_budget_usage_rate', defaultValue: '' },
        }
      },
      sales: {
        type: 'object',
        path: 'sales',
        schema: {
          target: { type: 'primitive', path: 'target', defaultValue: '' },
          current: { type: 'primitive', path: 'current', defaultValue: '' },
          progress: { type: 'primitive', path: 'progress', defaultValue: '' },
        }
      },
      cvr: {
        type: 'object',
        path: 'cvr',
        schema: {
          target: { type: 'primitive', path: 'target', defaultValue: '' },
          current: { type: 'primitive', path: 'current', defaultValue: '' },
          diff: { type: 'primitive', path: 'diff', defaultValue: '' },
        }
      },
      acos: {
        type: 'object',
        path: 'acos',
        schema: {
          target: { type: 'primitive', path: 'target', defaultValue: '' },
          current: { type: 'primitive', path: 'current', defaultValue: '' },
          diff: { type: 'primitive', path: 'diff', defaultValue: '' },
        }
      },
      week_performance_vs_target: { type: 'primitive', path: 'week_performance_vs_target', defaultValue: '' },
      key_observations: {
        type: 'array',
        path: 'key_observations'
        // 没有 schema，表示数组元素是原始字符串值
      },
    }
  },
  ai_feedbackContent: {
    type: 'primitive',
    path: 'ai_feedbackContent',
    defaultValue: ''
  }
};

// 为 DailyAdjustmentProposal 创建映射配置
export const dailyAdjustmentProposalConfig: { [K in keyof Strategy.DailyAdjustmentProposal]: Mapping } = {
  trigger_hour: {
    type: 'primitive',
    path: 'trigger_hour',
    defaultValue: ''
  },
  overall_rationale: {
    type: 'primitive',
    path: 'overall_rationale',
    defaultValue: ''
  },
  adjustments: {
    type: 'object',
    path: 'adjustments',
    schema: {
      daily_budget: {
        type: 'object',
        path: 'daily_budget',
        schema: {
          old: { type: 'primitive', path: 'old', defaultValue: '' },
          new: { type: 'primitive', path: 'new', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
        }
      },
      campaign: {
        type: 'array',
        path: 'campaign',
        schema: {
          campaign_id: { type: 'primitive', path: 'campaign_id', defaultValue: '' },
          campaign_name: { type: 'primitive', path: 'campaign_name', defaultValue: '' },
          budget_old: { type: 'primitive', path: 'budget_old', defaultValue: '' },
          budget_new: { type: 'primitive', path: 'budget_new', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
          bid_old: { type: 'primitive', path: 'bid_old', defaultValue: '' },
          bid_new: { type: 'primitive', path: 'bid_new', defaultValue: '' },
          current_acos: { type: 'primitive', path: 'current_acos', defaultValue: '' },
          current_cvr: { type: 'primitive', path: 'current_cvr', defaultValue: '' },
          current_sales: { type: 'primitive', path: 'current_sales', defaultValue: '' },
          current_spend: { type: 'primitive', path: 'current_spend', defaultValue: '' },
          swd_acos: { type: 'primitive', path: 'swd_acos', defaultValue: '' }
        }
      },
      placement: {
        type: 'array',
        path: 'placement',
        schema: {
          type: { type: 'primitive', path: 'type', defaultValue: '' },
          old_bid: { type: 'primitive', path: 'old_bid', defaultValue: '' },
          new_bid: { type: 'primitive', path: 'new_bid', defaultValue: '' },
          adjustment_ratio_old: { type: 'primitive', path: 'adjustment_ratio_old', defaultValue: '' },
          adjustment_ratio_new: { type: 'primitive', path: 'adjustment_ratio_new', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
          current_acos: { type: 'primitive', path: 'current_acos', defaultValue: '' },
          current_cvr: { type: 'primitive', path: 'current_cvr', defaultValue: '' },
          current_sales: { type: 'primitive', path: 'current_sales', defaultValue: '' },
          current_spend: { type: 'primitive', path: 'current_spend', defaultValue: '' },
          swd_acos: { type: 'primitive', path: 'swd_acos', defaultValue: '' }
        }
      },
    }
  },
  day_progress_analysis: {
    type: 'object',
    path: 'day_progress_analysis',
    schema: {
      budget: {
        type: 'object',
        path: 'budget',
        schema: {
          day_budget: { type: 'primitive', path: 'day_budget', defaultValue: '' },
          day_budget_spend: { type: 'primitive', path: 'day_budget_spend', defaultValue: '' },
          day_budget_left: { type: 'primitive', path: 'day_budget_left', defaultValue: '' },
          current_spend_progress: { type: 'primitive', path: 'current_spend_progress', defaultValue: '' },
          target_spend_progress: { type: 'primitive', path: 'target_spend_progress', defaultValue: '' }
        }
      },
      sales: {
        type: 'object',
        path: 'sales',
        schema: {
          sales_target: { type: 'primitive', path: 'sales_target', defaultValue: '' },
          sales_current: { type: 'primitive', path: 'sales_current', defaultValue: '' },
          sales_left: { type: 'primitive', path: 'sales_left', defaultValue: '' },
          current_progress: { type: 'primitive', path: 'current_progress', defaultValue: '' },
          target_progress: { type: 'primitive', path: 'target_progress', defaultValue: '' }
        }
      },
      cvr: {
        type: 'object',
        path: 'cvr',
        schema: {
          target: { type: 'primitive', path: 'target', defaultValue: '' },
          current: { type: 'primitive', path: 'current', defaultValue: '' },
          diff: { type: 'primitive', path: 'diff', defaultValue: '' }
        }
      },
      acos: {
        type: 'object',
        path: 'acos',
        schema: {
          target: { type: 'primitive', path: 'target', defaultValue: '' },
          current: { type: 'primitive', path: 'current', defaultValue: '' },
          diff: { type: 'primitive', path: 'diff', defaultValue: '' }
        }
      },
      key_observations: {
        type: 'array',
        path: 'key_observations'
        // 没有 schema，表示数组元素是原始字符串值
      },
      day_performance_vs_target: { type: 'primitive', path: 'day_performance_vs_target', defaultValue: '' }
    }
  }
};

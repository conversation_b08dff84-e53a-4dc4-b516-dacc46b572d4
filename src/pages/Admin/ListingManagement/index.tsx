import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Table, Button, Select, Space, Dropdown, Image, ConfigProvider, Modal, message, Input, Form, Flex, Tooltip } from 'antd';
import {
  PlusOutlined,
  MoreOutlined,
  ExclamationCircleFilled,
  AmazonOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import AddListingModal from './components/AddListing';
import { getListingsList, batchDeleteListing, getListingFilterOptions, updateListing, getReportCreateStatus } from '@/services/ibidder_api/listings';
import { Link } from '@umijs/max';
import styles from './index.less';

const ListingManagement: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [, setPollingList] = useState<any[]>([]); // 需要轮询的临时数组
  const pollingTimerRef = React.useRef<NodeJS.Timeout | null>(null);
  const pollCountRef = React.useRef<number>(0); // 轮询计数器

  // 添加筛选选项状态
  const [filterOptions, setFilterOptions] = useState<{
    countries: string[];
    store_names: string[];
    owners: string[];
    currencies: string[];
    brands: string[];
    statuses: string[];
  }>({
    countries: [],
    store_names: [],
    owners: [],
    currencies: [],
    brands: [],
    statuses: []
  });

  // 添加筛选条件状态
  const [filters, setFilters] = useState<{
    country?: string;
    store_name?: string;
    owner?: string;
    asin?: string;
  }>({});

  // 在ListingManagement组件中添加分页状态
  const [pagination, setPagination] = useState<{
    current: number;
    pageSize: number;
    total: number;
  }>({
    current: 1,
    pageSize: 10,
    total: 0
  });

  const [form] = Form.useForm();

  // 新增：ASIN输入防抖定时器
  const asinDebounceTimer = React.useRef<NodeJS.Timeout | null>(null);
  // 新增：输入法合成状态
  const asinIsComposing = React.useRef(false);

  // 获取筛选选项
  const fetchFilterOptions = async () => {
    try {
      const response = await getListingFilterOptions();
      const responseData =response.data;

      if (responseData) {
        setFilterOptions({
          countries: responseData.countries || [],
          store_names: responseData.store_names || [],
          owners: responseData.owners || [],
          currencies: responseData.currencies || [],
          brands: responseData.brands || [],
          statuses: responseData.statuses || []
        });
      }
    } catch (error) {
      console.error('获取筛选选项出错:', error);
    }
  };

  // 轮询函数
  const pollReportStatus = React.useCallback(async (listToPoll: any[]) => {
    if (!listToPoll || listToPoll.length === 0) return;
    // 超过最大轮询次数则停止
    if (pollCountRef.current >= 60) {
      pollingTimerRef.current = null;
      message.warning('AI分析超时，请稍后重试或刷新页面。');
      return;
    }
    pollCountRef.current += 1;
    try {
      // 调用接口
      const resp: any = await getReportCreateStatus({ data: listToPoll });
      const respData = resp?.data || [];
      if (!Array.isArray(respData)) return;

      // 找到report_status为true的项
      const finished = respData.filter((item: any) => item.report_status === true);
      if (finished.length > 0) {
        // 更新tableData中对应项的report_status为true
        setTableData(prev => prev.map(row => {
          const match = finished.find(
            (f: any) => f.parent_asin === row.parent_asin && f.country === row.country && f.profile_id === row.profile_id
          );
          if (match) {
            return { ...row, report_status: true };
          }
          return row;
        }));
      }
      // 从listToPoll中剔除已完成的项
      const unfinished = listToPoll.filter((row: any) => {
        return !finished.some(
          (f: any) => f.parent_asin === row.parent_asin && f.country === row.country && f.profile_id === row.profile_id
        );
      });
      setPollingList(unfinished);
      if (unfinished.length > 0) {
        // 继续轮询
        pollingTimerRef.current = setTimeout(() => {
          pollReportStatus(unfinished);
        }, 10000); // 10秒
      } else {
        // 轮询结束
        pollingTimerRef.current = null;
      }
    } catch (err) {
      console.log('轮询失败', err);
      return;
      // pollingTimerRef.current = setTimeout(() => {
      //   pollReportStatus(listToPoll);
      // }, 10000);
    }
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (pollingTimerRef.current) {
        clearTimeout(pollingTimerRef.current);
        pollingTimerRef.current = null;
      }
    };
  }, []);

  // 获取列表数据
  const fetchListingsData = async (values?: any) => {
    setLoading(true);
    try {
      // 构建API参数，加入筛选条件
      const params: any = {
        // 添加分页参数
        page: pagination.current,
        page_size: pagination.pageSize
      };
      if (values) {
        // 添加筛选参数
        if (values.country) params.country = values.country;
        if (values.store_name) params.store_name = values.store_name;
        if (values.owner) params.owner = values.owner;
        if (values.asin) params.asin = values.asin;
      } else {
        params.country = filters.country;
        params.store_name = filters.store_name;
        params.owner = filters.owner;
        params.asin = filters.asin;
      }

      const response: any = await getListingsList(params);

      // 检查响应结构
      if (response.code === 200) {
        const responseData = response.data;
        setTableData(responseData.listings);
        // 更新分页信息
        setPagination(prev => ({
          ...prev,
          total: responseData.total
        }));
        // 轮询逻辑：筛选report_status为false的项
        const needPoll = (responseData.listings || []).filter((item: any) => item.report_status === false)
          .map((item: any) => ({ parent_asin: item.parent_asin, country: item.country, profile_id: item.profile_id }));
        // 清理上一次定时器
        if (pollingTimerRef.current) {
          clearTimeout(pollingTimerRef.current);
          pollingTimerRef.current = null;
        }
        pollCountRef.current = 0; // 重置轮询计数
        setPollingList(needPoll);
        if (needPoll.length > 0) {
          pollReportStatus(needPoll);
        }
      } else {
        message.error('获取列表数据失败');
      }
    } catch (error) {
      console.error('获取Listings数据出错:', error);
      message.error('获取列表数据异常');
      // 如果API调用失败，返回空数组
      setTableData([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理删除按钮点击
  const handleDeleteClick = ({ parent_asin, profile_id }: { parent_asin: string; profile_id: number }) => {
    Modal.confirm({
      title: '确认将此商品从列表中删除？',
      icon: <ExclamationCircleFilled />,
      content: '删除后，您可通过添加listing功能重新添加至列表',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      width: 500,
      async onOk() {
        try {
          // 构建删除参数
          const deleteParams = {
            parent_asin,
            profile_id
          };

          // 调用删除API
          const result: any = await batchDeleteListing({ listings: [deleteParams] });
          if (result?.data?.success_count > 0) {
            message.success('删除成功');
            // 从表格数据中移除对应的记录
            fetchListingsData();
            return;
          } else {
            message.error('删除失败');
          }
        } catch (error) {
          console.error('删除操作出错:', error);
          message.error('删除操作异常');
        }
      }
    });
  };

  // 处理负责人更新
  const handleOwnerChange = async (value: string, record: any) => {
    try {
      const updateParams = {
        parent_asin: record.parent_asin,
        profile_id: record.profile_id,
        owners: [value]
      };

      await updateListing(updateParams);
      message.success('负责人更新成功');
      fetchListingsData();
    } catch (error) {
      console.error('更新负责人出错:', error);
      message.error('更新负责人异常');
    }
  };

  // 定义表格列
  const tableColumns = [
    {
      key: 'image_url',
      title: '图片',
      dataIndex: 'image_url',
      width: 80,
      render: (text: string) => <Image src={text} width={60} />,
    },
    {
      key: 'parent_asin',
      title: 'ASIN',
      dataIndex: 'parent_asin',
      width: 160,
      render: (text: string, record: any) => {
        return (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            { record.report_status ? (
              <Link
                className={styles.linkItem}
                to={`/ListingDetail?asin=${record.parent_asin}&profile_id=${record.profile_id}`}
                target="_blank"
                >{text}</Link>
            ): (
              <span style={{color: '#4E5969'}}>{text}</span>
            ) }

            <a href={record.url} target="_blank" rel="noreferrer" className={styles.amazonIcon}>
              <AmazonOutlined style={{width: 12, height: 12}} />
            </a>
          </div>
        );
      },
    },
    {
      key: 'title',
      title: '标题',
      dataIndex: 'title',
      ellipsis: true,
    },
    {
      key: 'store_name',
      title: '店铺',
      dataIndex: 'store_name',
      width: 160,
    },
    {
      key: 'country',
      title: '国家',
      dataIndex: 'country',
      width: 74,
    },
    {
      key: 'execution_status',
      title: (
        <>
          <span>执行状态</span>
            <Tooltip
              placement="top"
              title={
                <div>
                  <div style={{ fontWeight: 'bold', marginBottom: 8 }}>为AI广告执行状态</div>
                  <div style={{ marginBottom: 4 }}>已开启: AI已根据最新日广告策略,自动调整此产品的亚马逊广告;</div>
                  <div style={{ marginBottom: 4 }}>已关闭: AI不执行亚马逊广告,只做广告策略</div>
                  <div>您可以在详情页中的设置功能进行开启/关闭操作。</div>
                </div>
              }
              overlayInnerStyle={{ width: '330px' }}
            >
            <QuestionCircleOutlined style={{marginLeft: 4}} />
          </Tooltip>
        </>
      ),
      dataIndex: 'setting',
      width: 110,
      render: (setting: any) => {
        const isEnabled = setting?.ads_operation;
        return (
          <div 
            style={{
              width: 70,
              display: 'flex',
              alignItems: 'center',
              backgroundColor: isEnabled ? '#E8FFEA' : '#E2E8F0',
              padding: '5px 8px',
              borderRadius: 4
            }}>
            <div
              style={{
                width: 4,
                height: 4,
                borderRadius: '50%',
                backgroundColor: isEnabled ? '#00B42A' : '#86909C',
                marginRight: 5
              }}
            />
            <span style={{ color: isEnabled ? '#00B42A' : '#86909C' }}>
              {isEnabled ? '已开启' : '已关闭'}
            </span>
          </div>
        );
      },
    },
    {
      key: 'owners',
      title: '负责人',
      dataIndex: 'owners',
      width: 140,
      render: (text: string, record: any) => (
        <Select
          value={record.owners}
          style={{ width: '100%' }}
          placeholder="选择负责人"
          onChange={(value) => handleOwnerChange(value, record)}
          size="small"
        >
          {filterOptions.owners.map(owner => (
            <Select.Option key={owner} value={owner}>{owner}</Select.Option>
          ))}
        </Select>
      ),
    },
    {
      key: 'action',
      title: '操作',
      align: 'center' as const,
      width: 160,
      render: (_: any, record: any) => {
        if (record.report_status === false) {
          return <span style={{ color: '#4E5969' }}>AI分析中请稍后...</span>;
        }
        return (
          <Space size="small">
            <Link to={`/ListingDetail?asin=${record.parent_asin}&profile_id=${record.profile_id}`} target="_blank">分析</Link>
            <Dropdown menu={{
              items: [
                {
                  key: 'delete',
                  label: '删除',
                  onClick: () => handleDeleteClick({ parent_asin: record.parent_asin, profile_id: record.profile_id })
                }
              ]
            }}>
              <Button type="text" size="small" onClick={e => e.preventDefault()}>
                <MoreOutlined />
              </Button>
            </Dropdown>
          </Space>
        );
      },
    },
  ];

  // Placeholder functions for actions
  const handleAddListing = () => {
    setIsModalOpen(false);
    fetchListingsData();
    // Add logic to actually add listing and refresh table
  };

  // 处理分页变化
  const handleTableChange = (newPagination: any) => {
    setPagination(prev => ({
      ...prev,
      current: newPagination.current,
      pageSize: newPagination.pageSize
    }));

    // 此处不直接调用fetchListingsData，利用useEffect监听pagination变化
  };

  // 监听分页变化，调用API
  useEffect(() => {
    fetchListingsData();
  }, [pagination.current, pagination.pageSize]);

  // 初始化时获取筛选选项和列表数据
  useEffect(() => {
    fetchFilterOptions();
  }, []);

  // 根据筛选条件搜索
  const handleSearch = (values: any) => {
    // 更新filters状态
    setFilters(values);

    // 重置分页到第一页
    setPagination(prev => ({
      ...prev,
      current: 1
    }));

    fetchListingsData(values);
  };

  const FilterArea = () => (
    <Form
      form={form}
      layout="inline"
      onValuesChange={(changedValues, allValues) => {
        // 只有非asin字段变化时才自动搜索
        if (Object.keys(changedValues).includes('asin')) return;
        handleSearch(allValues);
      }}
    >
      <Form.Item name="country" initialValue={filters.country}>
        <Select
          style={{ width: 150 }}
          placeholder="全部国家"
          allowClear
        >
          {filterOptions.countries.map(country => (
            <Select.Option key={country} value={country}>{country}</Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="store_name" initialValue={filters.store_name}>
        <Select
          style={{ width: 150 }}
          placeholder="全部店铺"
          allowClear
        >
          {filterOptions.store_names.map(store => (
            <Select.Option key={store} value={store}>{store}</Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="owner" initialValue={filters.owner}>
        <Select
          style={{ width: 150 }}
          placeholder="Listing负责人"
          allowClear
        >
          {filterOptions.owners.map(owner => (
            <Select.Option key={owner} value={owner}>{owner}</Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="asin" initialValue={filters.asin}>
        <Input
          className={styles.asinInputFixed}
          style={{ width: 150, height: 32, lineHeight: '32px' }}
          placeholder="搜索ASIN"
          allowClear
          onCompositionStart={() => { asinIsComposing.current = true; }}
          onCompositionEnd={() => { asinIsComposing.current = false; }}
          onChange={e => {
            const value = e.target.value;
            // 清空已有定时器
            if (asinDebounceTimer.current) {
              clearTimeout(asinDebounceTimer.current);
              asinDebounceTimer.current = null;
            }
            if (value === '' || value === undefined) {
              // 清空时立即搜索
              handleSearch(form.getFieldsValue());
            } else {
              // 输入后2秒自动搜索
              asinDebounceTimer.current = setTimeout(() => {
                handleSearch(form.getFieldsValue());
              }, 2000);
            }
          }}
          onPressEnter={() => {
            // 只有非输入法合成状态下才触发搜索
            if (asinIsComposing.current) return;
            if (asinDebounceTimer.current) {
              clearTimeout(asinDebounceTimer.current);
              asinDebounceTimer.current = null;
            }
            handleSearch(form.getFieldsValue());
          }}
          onBlur={() => {
            // 失去焦点时也进行搜索
            if (asinDebounceTimer.current) {
              clearTimeout(asinDebounceTimer.current);
              asinDebounceTimer.current = null;
            }
            handleSearch(form.getFieldsValue());
          }}
        />
      </Form.Item>
    </Form>
  );

  return (
    <PageContainer title={false}>
      <Card>
        <Flex justify='space-between' style={{marginBottom: 16}}>
          <FilterArea />
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsModalOpen(true)}>
            添加Listing
          </Button>
        </Flex>
        <ConfigProvider renderEmpty={() =>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsModalOpen(true)}>添加Listing</Button>
            <p style={{ marginTop: 16 }}>暂无listing，点击上方按钮，进行添加～</p>
          </div>
        }>
          <Table
            columns={tableColumns}
            dataSource={tableData}
            loading={loading}
            rowKey="parent_asin" // 明确指定使用asin作为行的唯一标识
            onChange={handleTableChange} // 添加表格变化事件处理
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
            }}
          />
        </ConfigProvider>
      </Card>
      <AddListingModal
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={handleAddListing}
      />
    </PageContainer>
  );
};

export default ListingManagement;

.addListingForm, .settingForm{
  .colTitle{
    margin-top: 8px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    .colTitleText{
      font-size: 16px;
      font-weight: bold;
    }
    .colTitleLink{
      margin-left: auto;
    }
  }
  .productCard{
    height: 500px;
    overflow-y: auto;
    border: none;
    background-color: #F9FAFB;
    :global{
      .ant-card-body{
        padding: 16px;
      }
    }
  }
  .productInfo{
    display: flex;
    align-items: flex-start;
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
  }
  .formCard{
    &:last-child{
      margin-bottom: 0;
    }
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
    :global {
      .ant-form-item {
        margin-bottom: 16px;
      }
    }
    .formItem{
      :global{
        .ant-form-item-label {
          border-bottom: 1px solid #F2F3F5;
          margin-bottom: 16px !important;
          font-weight: bold;
        }
      }
    }
  }
  .promotionCard{ 
    padding: 16px;
    margin-bottom: 16px;
    border: 1px dashed #D0D5DD;
    background-color: #F9FAFB;
    border-radius: 8px;
  }
  .addPromotionButton{
    background-color: #F9FAFB;
    border-color: #EAECF0;
  }
  .settingWrapper{
    display: flex;
    flex-direction: column;
    gap: 16px;
    border-radius: 8px;
    background-color: #F9FAFB;
    padding: 20px;
    .settingCard{
      display: flex;
      align-items: center;
      padding: 16px;
      background-color: #fff;
      border-radius: 8px;
    }
    .settingCardTitle{
      width: 160px;
      font-weight: bold;
      color: #101828;
    }
    .settingCardDivider {
      height: 50px;
    }
    .settingCardInputWrapper{
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 280px;
      margin: 0 24px;
    }
    .settingCardInput {
      width: 125px;
      margin-bottom: 0;
      position: relative;
      // 新增绝对定位的校验提示样式
      :global {
        .ant-form-item-explain {
          position: absolute;
          left: 0;
          top: 100%;
          z-index: 2;
          background: transparent;
          color: #ff4d4f;
          font-size: 12px;
          line-height: 1.5;
          margin-top: 2px;
          white-space: nowrap;
          pointer-events: none;
        }
      }
    }
    .targetWrapper{
      display: flex;
      gap: 30px;
      min-width: 280px;
      margin: 0 24px;
    }
    .targetItem {
      display: flex;
      align-items: center;
      .targetItemTitle{
        width: 100px;
      }
      .targetItemValue{
        flex: 1;
      }
    }
    .settingCardDesc{
      flex: 1;
      color: #86909C;
      font-size: 13px;
    }
  }
}